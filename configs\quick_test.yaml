# Quick test configuration for PPO training on MiniGrid

# Environment settings
env_name: 'MiniGrid-Empty-5x5-v0'
flatten_obs: true
normalize_obs: true
max_episode_steps: 100

# Training settings (reduced for quick testing)
total_timesteps: 50000
learning_rate: 0.0003
batch_size: 32
n_epochs: 2
n_steps: 512
gamma: 0.99
gae_lambda: 0.95
clip_epsilon: 0.2
value_coef: 0.5
entropy_coef: 0.01
max_grad_norm: 0.5

# Network settings
hidden_dim: 128

# Logging and saving (more frequent for testing)
log_interval: 2
save_interval: 20
eval_interval: 10
eval_episodes: 5
save_dir: 'checkpoints'
log_dir: 'logs'
use_wandb: false
wandb_project: 'llmae-ppo-test'

# Device settings
device: 'cpu'  # Use CPU for quick testing
seed: 42
