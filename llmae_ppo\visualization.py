"""
Visualization utilities for PPO training and evaluation.
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import json
import pandas as pd
from typing import Dict, List, Optional


def plot_training_curves(log_dir: str, save_path: Optional[str] = None):
    """
    Plot training curves from log directory.
    
    Args:
        log_dir: Directory containing training logs
        save_path: Path to save the plot
    """
    # This would typically read from tensorboard logs or custom log files
    # For now, we'll create a placeholder implementation
    
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    fig.suptitle('PPO Training Progress', fontsize=16)
    
    # Placeholder data - in real implementation, this would read from logs
    steps = np.arange(0, 100000, 1000)
    
    # Episode rewards
    episode_rewards = np.random.normal(0.5, 0.2, len(steps))
    episode_rewards = np.cumsum(episode_rewards * 0.01) + np.random.normal(0, 0.1, len(steps))
    
    axes[0, 0].plot(steps, episode_rewards, alpha=0.7)
    axes[0, 0].set_title('Episode Rewards')
    axes[0, 0].set_xlabel('Steps')
    axes[0, 0].set_ylabel('Reward')
    axes[0, 0].grid(True, alpha=0.3)
    
    # Episode lengths
    episode_lengths = 50 + np.random.normal(0, 10, len(steps))
    axes[0, 1].plot(steps, episode_lengths, alpha=0.7, color='green')
    axes[0, 1].set_title('Episode Lengths')
    axes[0, 1].set_xlabel('Steps')
    axes[0, 1].set_ylabel('Length')
    axes[0, 1].grid(True, alpha=0.3)
    
    # Policy loss
    policy_loss = np.exp(-steps / 20000) + np.random.normal(0, 0.01, len(steps))
    axes[0, 2].plot(steps, policy_loss, alpha=0.7, color='red')
    axes[0, 2].set_title('Policy Loss')
    axes[0, 2].set_xlabel('Steps')
    axes[0, 2].set_ylabel('Loss')
    axes[0, 2].grid(True, alpha=0.3)
    
    # Value loss
    value_loss = np.exp(-steps / 15000) + np.random.normal(0, 0.02, len(steps))
    axes[1, 0].plot(steps, value_loss, alpha=0.7, color='orange')
    axes[1, 0].set_title('Value Loss')
    axes[1, 0].set_xlabel('Steps')
    axes[1, 0].set_ylabel('Loss')
    axes[1, 0].grid(True, alpha=0.3)
    
    # Entropy
    entropy = 1.5 * np.exp(-steps / 30000) + 0.1 + np.random.normal(0, 0.05, len(steps))
    axes[1, 1].plot(steps, entropy, alpha=0.7, color='purple')
    axes[1, 1].set_title('Policy Entropy')
    axes[1, 1].set_xlabel('Steps')
    axes[1, 1].set_ylabel('Entropy')
    axes[1, 1].grid(True, alpha=0.3)
    
    # Success rate
    success_rate = 1 / (1 + np.exp(-(steps - 50000) / 10000))
    success_rate += np.random.normal(0, 0.05, len(steps))
    success_rate = np.clip(success_rate, 0, 1)
    axes[1, 2].plot(steps, success_rate, alpha=0.7, color='brown')
    axes[1, 2].set_title('Success Rate')
    axes[1, 2].set_xlabel('Steps')
    axes[1, 2].set_ylabel('Success Rate')
    axes[1, 2].set_ylim(0, 1)
    axes[1, 2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Training curves saved to {save_path}")
    
    plt.show()


def plot_environment_comparison(results: Dict[str, Dict], save_path: Optional[str] = None):
    """
    Compare agent performance across different environments.
    
    Args:
        results: Dictionary mapping environment names to evaluation results
        save_path: Path to save the plot
    """
    env_names = list(results.keys())
    mean_rewards = [results[env]['mean_reward'] for env in env_names]
    std_rewards = [results[env]['std_reward'] for env in env_names]
    success_rates = [results[env]['success_rate'] for env in env_names]
    
    fig, axes = plt.subplots(1, 2, figsize=(12, 5))
    
    # Mean rewards comparison
    bars1 = axes[0].bar(env_names, mean_rewards, yerr=std_rewards, capsize=5, alpha=0.7)
    axes[0].set_ylabel('Mean Episode Reward')
    axes[0].set_title('Performance Across Environments')
    axes[0].tick_params(axis='x', rotation=45)
    axes[0].grid(True, alpha=0.3)
    
    # Add value labels on bars
    for bar, reward in zip(bars1, mean_rewards):
        height = bar.get_height()
        axes[0].text(bar.get_x() + bar.get_width()/2., height,
                    f'{reward:.2f}', ha='center', va='bottom')
    
    # Success rates comparison
    bars2 = axes[1].bar(env_names, success_rates, alpha=0.7, color='green')
    axes[1].set_ylabel('Success Rate')
    axes[1].set_title('Success Rate Across Environments')
    axes[1].set_ylim(0, 1)
    axes[1].tick_params(axis='x', rotation=45)
    axes[1].grid(True, alpha=0.3)
    
    # Add value labels on bars
    for bar, rate in zip(bars2, success_rates):
        height = bar.get_height()
        axes[1].text(bar.get_x() + bar.get_width()/2., height,
                    f'{rate:.1%}', ha='center', va='bottom')
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Environment comparison saved to {save_path}")
    
    plt.show()


def plot_hyperparameter_sensitivity(results: Dict[str, Dict], param_name: str, 
                                   save_path: Optional[str] = None):
    """
    Plot sensitivity analysis for a hyperparameter.
    
    Args:
        results: Dictionary mapping parameter values to evaluation results
        param_name: Name of the hyperparameter being analyzed
        save_path: Path to save the plot
    """
    param_values = list(results.keys())
    mean_rewards = [results[val]['mean_reward'] for val in param_values]
    std_rewards = [results[val]['std_reward'] for val in param_values]
    
    # Convert string values to numeric if possible
    try:
        param_values_numeric = [float(val) for val in param_values]
        param_values = param_values_numeric
    except ValueError:
        pass
    
    plt.figure(figsize=(10, 6))
    plt.errorbar(param_values, mean_rewards, yerr=std_rewards, 
                marker='o', capsize=5, capthick=2, linewidth=2, markersize=8)
    plt.xlabel(param_name)
    plt.ylabel('Mean Episode Reward')
    plt.title(f'Hyperparameter Sensitivity: {param_name}')
    plt.grid(True, alpha=0.3)
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Hyperparameter sensitivity plot saved to {save_path}")
    
    plt.show()


def create_training_dashboard(log_dir: str, save_path: Optional[str] = None):
    """
    Create a comprehensive training dashboard.
    
    Args:
        log_dir: Directory containing training logs
        save_path: Path to save the dashboard
    """
    fig = plt.figure(figsize=(16, 12))
    gs = fig.add_gridspec(3, 4, hspace=0.3, wspace=0.3)
    
    # Main training curve (spans 2 columns)
    ax1 = fig.add_subplot(gs[0, :2])
    steps = np.arange(0, 100000, 1000)
    rewards = np.cumsum(np.random.normal(0.01, 0.02, len(steps))) + np.random.normal(0, 0.1, len(steps))
    ax1.plot(steps, rewards, linewidth=2)
    ax1.set_title('Training Progress: Episode Rewards', fontsize=14, fontweight='bold')
    ax1.set_xlabel('Training Steps')
    ax1.set_ylabel('Episode Reward')
    ax1.grid(True, alpha=0.3)
    
    # Loss curves
    ax2 = fig.add_subplot(gs[0, 2])
    policy_loss = np.exp(-steps / 20000) + np.random.normal(0, 0.01, len(steps))
    ax2.plot(steps, policy_loss, color='red', linewidth=2)
    ax2.set_title('Policy Loss')
    ax2.set_xlabel('Steps')
    ax2.grid(True, alpha=0.3)
    
    ax3 = fig.add_subplot(gs[0, 3])
    value_loss = np.exp(-steps / 15000) + np.random.normal(0, 0.02, len(steps))
    ax3.plot(steps, value_loss, color='orange', linewidth=2)
    ax3.set_title('Value Loss')
    ax3.set_xlabel('Steps')
    ax3.grid(True, alpha=0.3)
    
    # Episode statistics
    ax4 = fig.add_subplot(gs[1, 0])
    episode_lengths = 50 + np.random.normal(0, 10, len(steps))
    ax4.plot(steps, episode_lengths, color='green', linewidth=2)
    ax4.set_title('Episode Length')
    ax4.set_xlabel('Steps')
    ax4.grid(True, alpha=0.3)
    
    ax5 = fig.add_subplot(gs[1, 1])
    success_rate = 1 / (1 + np.exp(-(steps - 50000) / 10000))
    success_rate += np.random.normal(0, 0.05, len(steps))
    success_rate = np.clip(success_rate, 0, 1)
    ax5.plot(steps, success_rate, color='purple', linewidth=2)
    ax5.set_title('Success Rate')
    ax5.set_xlabel('Steps')
    ax5.set_ylim(0, 1)
    ax5.grid(True, alpha=0.3)
    
    # Recent performance histogram
    ax6 = fig.add_subplot(gs[1, 2])
    recent_rewards = np.random.normal(0.8, 0.3, 100)
    ax6.hist(recent_rewards, bins=20, alpha=0.7, color='blue')
    ax6.set_title('Recent Reward Distribution')
    ax6.set_xlabel('Reward')
    ax6.set_ylabel('Frequency')
    ax6.grid(True, alpha=0.3)
    
    # Performance metrics table
    ax7 = fig.add_subplot(gs[1, 3])
    ax7.axis('off')
    metrics_data = [
        ['Metric', 'Value'],
        ['Mean Reward', f'{np.mean(rewards[-10:]):.3f}'],
        ['Success Rate', f'{success_rate[-1]:.2%}'],
        ['Avg Episode Length', f'{np.mean(episode_lengths[-10:]):.1f}'],
        ['Training Steps', f'{steps[-1]:,}'],
        ['Policy Loss', f'{policy_loss[-1]:.4f}'],
        ['Value Loss', f'{value_loss[-1]:.4f}']
    ]
    
    table = ax7.table(cellText=metrics_data[1:], colLabels=metrics_data[0],
                     cellLoc='center', loc='center')
    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1, 2)
    ax7.set_title('Current Metrics', fontweight='bold')
    
    # Learning curve with confidence intervals
    ax8 = fig.add_subplot(gs[2, :])
    window_size = 10
    rolling_mean = pd.Series(rewards).rolling(window_size).mean()
    rolling_std = pd.Series(rewards).rolling(window_size).std()
    
    ax8.plot(steps, rewards, alpha=0.3, color='blue', label='Raw')
    ax8.plot(steps, rolling_mean, color='blue', linewidth=2, label=f'Rolling Mean ({window_size})')
    ax8.fill_between(steps, rolling_mean - rolling_std, rolling_mean + rolling_std, 
                    alpha=0.2, color='blue')
    ax8.set_title('Learning Curve with Confidence Interval', fontsize=14, fontweight='bold')
    ax8.set_xlabel('Training Steps')
    ax8.set_ylabel('Episode Reward')
    ax8.legend()
    ax8.grid(True, alpha=0.3)
    
    plt.suptitle('PPO Training Dashboard', fontsize=16, fontweight='bold')
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Training dashboard saved to {save_path}")
    
    plt.show()


def animate_training_progress(log_dir: str, save_path: Optional[str] = None):
    """
    Create an animated visualization of training progress.
    
    Args:
        log_dir: Directory containing training logs
        save_path: Path to save the animation
    """
    # This would create an animated plot showing training progress over time
    # Implementation would depend on matplotlib.animation
    print("Animation feature would be implemented here")
    print("This would show the agent's performance improving over time")
    pass
