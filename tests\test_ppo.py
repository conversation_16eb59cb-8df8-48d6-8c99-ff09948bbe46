"""
Unit tests for PPO agent and trainer.
"""

import pytest
import torch
import numpy as np
from unittest.mock import Mock, patch
from llmae_ppo.ppo import PPOAgent, PPOTrainer


class TestPPOAgent:
    """Test cases for PPOAgent."""
    
    def test_agent_creation(self):
        """Test PPO agent creation."""
        state_dim = (147,)
        action_dim = 7
        
        agent = PPOAgent(state_dim, action_dim, device='cpu')
        
        assert agent is not None
        assert agent.device == 'cpu'
        assert agent.action_dim == action_dim
        assert agent.policy_net is not None
        assert agent.value_net is not None
        assert agent.policy_optimizer is not None
        assert agent.value_optimizer is not None
    
    def test_get_action(self):
        """Test action selection."""
        state_dim = (147,)
        action_dim = 7
        
        agent = PPOAgent(state_dim, action_dim, device='cpu')
        
        # Create random state
        state = np.random.randn(*state_dim)
        
        # Get action
        action, log_prob = agent.get_action(state)
        
        assert isinstance(action, (int, np.integer))
        assert isinstance(log_prob, (float, np.floating))
        assert 0 <= action < action_dim
        assert not np.isnan(log_prob)
        assert not np.isinf(log_prob)
    
    def test_get_value(self):
        """Test value estimation."""
        state_dim = (147,)
        action_dim = 7
        
        agent = PPOAgent(state_dim, action_dim, device='cpu')
        
        # Create random state
        state = np.random.randn(*state_dim)
        
        # Get value
        value = agent.get_value(state)
        
        assert isinstance(value, (float, np.floating))
        assert not np.isnan(value)
        assert not np.isinf(value)
    
    def test_save_and_load(self, tmp_path):
        """Test model saving and loading."""
        state_dim = (147,)
        action_dim = 7
        
        # Create and train agent slightly
        agent1 = PPOAgent(state_dim, action_dim, device='cpu')
        
        # Get initial parameters
        initial_policy_params = list(agent1.policy_net.parameters())[0].clone()
        initial_value_params = list(agent1.value_net.parameters())[0].clone()
        
        # Modify parameters slightly (simulate training)
        with torch.no_grad():
            for param in agent1.policy_net.parameters():
                param.add_(torch.randn_like(param) * 0.01)
            for param in agent1.value_net.parameters():
                param.add_(torch.randn_like(param) * 0.01)
        
        # Save model
        save_path = tmp_path / "test_model.pt"
        agent1.save(str(save_path))
        
        # Create new agent and load
        agent2 = PPOAgent(state_dim, action_dim, device='cpu')
        agent2.load(str(save_path))
        
        # Check that parameters match
        loaded_policy_params = list(agent2.policy_net.parameters())[0]
        loaded_value_params = list(agent2.value_net.parameters())[0]
        
        assert torch.allclose(loaded_policy_params, list(agent1.policy_net.parameters())[0])
        assert torch.allclose(loaded_value_params, list(agent1.value_net.parameters())[0])
        assert not torch.allclose(loaded_policy_params, initial_policy_params)
        assert not torch.allclose(loaded_value_params, initial_value_params)


class TestPPOTrainer:
    """Test cases for PPOTrainer."""
    
    def test_trainer_creation(self):
        """Test PPO trainer creation."""
        state_dim = (147,)
        action_dim = 7
        
        agent = PPOAgent(state_dim, action_dim, device='cpu')
        trainer = PPOTrainer(agent)
        
        assert trainer is not None
        assert trainer.agent is agent
        assert trainer.gamma == 0.99
        assert trainer.gae_lambda == 0.95
        assert trainer.clip_epsilon == 0.2
    
    def test_compute_gae(self):
        """Test Generalized Advantage Estimation computation."""
        state_dim = (147,)
        action_dim = 7
        
        agent = PPOAgent(state_dim, action_dim, device='cpu')
        trainer = PPOTrainer(agent)
        
        # Create sample data
        rewards = [1.0, 0.0, 1.0, 0.0, 1.0]
        values = [0.5, 0.3, 0.7, 0.2, 0.8]
        dones = [False, False, False, False, True]
        next_value = 0.0
        
        # Compute GAE
        advantages = trainer.compute_gae(rewards, values, dones, next_value)
        
        assert len(advantages) == len(rewards)
        assert all(isinstance(adv, float) for adv in advantages)
        assert not any(np.isnan(adv) for adv in advantages)
        assert not any(np.isinf(adv) for adv in advantages)
    
    def test_update(self):
        """Test PPO update."""
        state_dim = (147,)
        action_dim = 7
        n_steps = 10
        
        agent = PPOAgent(state_dim, action_dim, device='cpu')
        trainer = PPOTrainer(agent)
        
        # Create sample trajectory data
        states = [np.random.randn(*state_dim) for _ in range(n_steps)]
        actions = [np.random.randint(0, action_dim) for _ in range(n_steps)]
        old_log_probs = [np.random.randn() for _ in range(n_steps)]
        rewards = [np.random.randn() for _ in range(n_steps)]
        dones = [False] * (n_steps - 1) + [True]
        
        # Get initial parameters
        initial_policy_params = list(agent.policy_net.parameters())[0].clone()
        initial_value_params = list(agent.value_net.parameters())[0].clone()
        
        # Update
        metrics = trainer.update(states, actions, old_log_probs, rewards, dones, epochs=1, batch_size=5)
        
        # Check that parameters changed
        updated_policy_params = list(agent.policy_net.parameters())[0]
        updated_value_params = list(agent.value_net.parameters())[0]
        
        assert not torch.allclose(initial_policy_params, updated_policy_params)
        assert not torch.allclose(initial_value_params, updated_value_params)
        
        # Check metrics
        assert 'policy_loss' in metrics
        assert 'value_loss' in metrics
        assert 'entropy_loss' in metrics
        assert all(isinstance(v, float) for v in metrics.values())
        assert all(not np.isnan(v) for v in metrics.values())
        assert all(not np.isinf(v) for v in metrics.values())
    
    def test_update_with_different_batch_sizes(self):
        """Test PPO update with different batch sizes."""
        state_dim = (147,)
        action_dim = 7
        n_steps = 20
        
        agent = PPOAgent(state_dim, action_dim, device='cpu')
        trainer = PPOTrainer(agent)
        
        # Create sample trajectory data
        states = [np.random.randn(*state_dim) for _ in range(n_steps)]
        actions = [np.random.randint(0, action_dim) for _ in range(n_steps)]
        old_log_probs = [np.random.randn() for _ in range(n_steps)]
        rewards = [np.random.randn() for _ in range(n_steps)]
        dones = [False] * (n_steps - 1) + [True]
        
        # Test different batch sizes
        for batch_size in [5, 10, 20]:
            metrics = trainer.update(states, actions, old_log_probs, rewards, dones, 
                                   epochs=1, batch_size=batch_size)
            
            assert 'policy_loss' in metrics
            assert 'value_loss' in metrics
            assert 'entropy_loss' in metrics
    
    def test_trainer_hyperparameters(self):
        """Test trainer with different hyperparameters."""
        state_dim = (147,)
        action_dim = 7
        
        agent = PPOAgent(state_dim, action_dim, device='cpu')
        
        # Test with custom hyperparameters
        trainer = PPOTrainer(
            agent,
            gamma=0.95,
            gae_lambda=0.9,
            clip_epsilon=0.1,
            value_coef=1.0,
            entropy_coef=0.02,
            max_grad_norm=1.0
        )
        
        assert trainer.gamma == 0.95
        assert trainer.gae_lambda == 0.9
        assert trainer.clip_epsilon == 0.1
        assert trainer.value_coef == 1.0
        assert trainer.entropy_coef == 0.02
        assert trainer.max_grad_norm == 1.0


class TestPPOIntegration:
    """Integration tests for PPO components."""
    
    def test_agent_trainer_integration(self):
        """Test integration between agent and trainer."""
        state_dim = (147,)
        action_dim = 7
        n_steps = 10
        
        # Create agent and trainer
        agent = PPOAgent(state_dim, action_dim, device='cpu')
        trainer = PPOTrainer(agent)
        
        # Simulate trajectory collection
        states = []
        actions = []
        old_log_probs = []
        rewards = []
        dones = []
        
        for _ in range(n_steps):
            state = np.random.randn(*state_dim)
            action, log_prob = agent.get_action(state)
            reward = np.random.randn()
            done = np.random.random() < 0.1  # 10% chance of episode end
            
            states.append(state)
            actions.append(action)
            old_log_probs.append(log_prob)
            rewards.append(reward)
            dones.append(done)
        
        # Ensure at least one episode ends
        dones[-1] = True
        
        # Update agent
        metrics = trainer.update(states, actions, old_log_probs, rewards, dones)
        
        # Verify update worked
        assert isinstance(metrics, dict)
        assert len(metrics) == 3
        assert all(key in metrics for key in ['policy_loss', 'value_loss', 'entropy_loss'])
    
    def test_multiple_updates(self):
        """Test multiple consecutive updates."""
        state_dim = (147,)
        action_dim = 7
        n_steps = 10
        n_updates = 5
        
        agent = PPOAgent(state_dim, action_dim, device='cpu')
        trainer = PPOTrainer(agent)
        
        for update in range(n_updates):
            # Generate new trajectory
            states = [np.random.randn(*state_dim) for _ in range(n_steps)]
            actions = [np.random.randint(0, action_dim) for _ in range(n_steps)]
            old_log_probs = [np.random.randn() for _ in range(n_steps)]
            rewards = [np.random.randn() for _ in range(n_steps)]
            dones = [False] * (n_steps - 1) + [True]
            
            # Update
            metrics = trainer.update(states, actions, old_log_probs, rewards, dones)
            
            # Verify metrics are reasonable
            assert all(isinstance(v, float) for v in metrics.values())
            assert all(not np.isnan(v) for v in metrics.values())
            assert all(not np.isinf(v) for v in metrics.values())


if __name__ == "__main__":
    pytest.main([__file__])
