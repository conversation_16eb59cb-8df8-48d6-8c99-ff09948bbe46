"""
LLMaE-PPO: A PyTorch implementation of Proximal Policy Optimization for MiniGrid environments.
"""

__version__ = "0.1.0"
__author__ = "LLMaE-PPO Team"

from .ppo import PPOAgent, PPOTrainer
from .networks import PolicyNetwork, ValueNetwork
from .env_wrapper import MiniGridWrapper
from .utils import Config, Logger

__all__ = [
    "PPOAgent",
    "PPOTrainer", 
    "PolicyNetwork",
    "ValueNetwork",
    "MiniGridWrapper",
    "Config",
    "Logger"
]
