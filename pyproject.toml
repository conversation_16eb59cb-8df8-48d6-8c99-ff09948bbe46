[project]
name = "llmae-ppo"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "gymnasium>=1.2.0",
    "matplotlib>=3.10.3",
    "minigrid>=3.0.0",
    "numpy>=2.3.1",
    "pillow>=11.3.0",
    "pyyaml>=6.0.2",
    "seaborn>=0.13.2",
    "tensorboard>=2.19.0",
    "torch>=2.7.1",
    "torchvision>=0.22.1",
    "tqdm>=4.67.1",
    "wandb>=0.21.0",
]

[dependency-groups]
dev = [
    "pytest>=8.4.1",
]
