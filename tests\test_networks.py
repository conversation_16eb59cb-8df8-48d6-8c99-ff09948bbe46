"""
Unit tests for neural networks.
"""

import pytest
import torch
import numpy as np
from llmae_ppo.networks import PolicyNetwork, ValueNetwork


class TestPolicyNetwork:
    """Test cases for PolicyNetwork."""
    
    def test_policy_network_creation_flattened(self):
        """Test policy network creation with flattened input."""
        input_dim = (147,)  # Flattened MiniGrid observation
        num_actions = 7
        
        policy_net = PolicyNetwork(input_dim, num_actions=num_actions)
        
        assert policy_net is not None
        assert policy_net.conv_layers is None  # Should be None for flattened input
        assert policy_net.fc_layers is not None
    
    def test_policy_network_creation_image(self):
        """Test policy network creation with image input."""
        input_dim = (3, 7, 7)  # MiniGrid image observation
        num_actions = 7
        
        policy_net = PolicyNetwork(input_dim, num_actions=num_actions)
        
        assert policy_net is not None
        assert policy_net.conv_layers is not None
        assert policy_net.fc_layers is not None
    
    def test_policy_network_forward_flattened(self):
        """Test forward pass with flattened input."""
        input_dim = (147,)
        num_actions = 7
        batch_size = 4
        
        policy_net = PolicyNetwork(input_dim, num_actions=num_actions)
        
        # Create random input
        x = torch.randn(batch_size, *input_dim)
        
        # Forward pass
        logits = policy_net(x)
        
        assert logits.shape == (batch_size, num_actions)
        assert not torch.isnan(logits).any()
        assert not torch.isinf(logits).any()
    
    def test_policy_network_forward_image(self):
        """Test forward pass with image input."""
        input_dim = (3, 7, 7)
        num_actions = 7
        batch_size = 4
        
        policy_net = PolicyNetwork(input_dim, num_actions=num_actions)
        
        # Create random input
        x = torch.randn(batch_size, *input_dim)
        
        # Forward pass
        logits = policy_net(x)
        
        assert logits.shape == (batch_size, num_actions)
        assert not torch.isnan(logits).any()
        assert not torch.isinf(logits).any()
    
    def test_get_action_and_log_prob(self):
        """Test action sampling and log probability calculation."""
        input_dim = (147,)
        num_actions = 7
        
        policy_net = PolicyNetwork(input_dim, num_actions=num_actions)
        
        # Create random state
        state = torch.randn(1, *input_dim)
        
        # Get action and log prob
        action, log_prob = policy_net.get_action_and_log_prob(state)
        
        assert action.shape == (1,)
        assert log_prob.shape == (1,)
        assert 0 <= action.item() < num_actions
        assert not torch.isnan(log_prob).any()
        assert not torch.isinf(log_prob).any()
    
    def test_get_log_prob(self):
        """Test log probability calculation for given actions."""
        input_dim = (147,)
        num_actions = 7
        batch_size = 4
        
        policy_net = PolicyNetwork(input_dim, num_actions=num_actions)
        
        # Create random state and actions
        state = torch.randn(batch_size, *input_dim)
        actions = torch.randint(0, num_actions, (batch_size,))
        
        # Get log probabilities
        log_probs = policy_net.get_log_prob(state, actions)
        
        assert log_probs.shape == (batch_size,)
        assert not torch.isnan(log_probs).any()
        assert not torch.isinf(log_probs).any()
    
    def test_get_entropy(self):
        """Test entropy calculation."""
        input_dim = (147,)
        num_actions = 7
        batch_size = 4
        
        policy_net = PolicyNetwork(input_dim, num_actions=num_actions)
        
        # Create random state
        state = torch.randn(batch_size, *input_dim)
        
        # Get entropy
        entropy = policy_net.get_entropy(state)
        
        assert entropy.shape == (batch_size,)
        assert (entropy >= 0).all()  # Entropy should be non-negative
        assert not torch.isnan(entropy).any()
        assert not torch.isinf(entropy).any()


class TestValueNetwork:
    """Test cases for ValueNetwork."""
    
    def test_value_network_creation_flattened(self):
        """Test value network creation with flattened input."""
        input_dim = (147,)
        
        value_net = ValueNetwork(input_dim)
        
        assert value_net is not None
        assert value_net.conv_layers is None
        assert value_net.fc_layers is not None
    
    def test_value_network_creation_image(self):
        """Test value network creation with image input."""
        input_dim = (3, 7, 7)
        
        value_net = ValueNetwork(input_dim)
        
        assert value_net is not None
        assert value_net.conv_layers is not None
        assert value_net.fc_layers is not None
    
    def test_value_network_forward_flattened(self):
        """Test forward pass with flattened input."""
        input_dim = (147,)
        batch_size = 4
        
        value_net = ValueNetwork(input_dim)
        
        # Create random input
        x = torch.randn(batch_size, *input_dim)
        
        # Forward pass
        values = value_net(x)
        
        assert values.shape == (batch_size,)
        assert not torch.isnan(values).any()
        assert not torch.isinf(values).any()
    
    def test_value_network_forward_image(self):
        """Test forward pass with image input."""
        input_dim = (3, 7, 7)
        batch_size = 4
        
        value_net = ValueNetwork(input_dim)
        
        # Create random input
        x = torch.randn(batch_size, *input_dim)
        
        # Forward pass
        values = value_net(x)
        
        assert values.shape == (batch_size,)
        assert not torch.isnan(values).any()
        assert not torch.isinf(values).any()
    
    def test_gradient_flow(self):
        """Test that gradients flow properly through the network."""
        input_dim = (147,)
        batch_size = 4
        
        value_net = ValueNetwork(input_dim)
        
        # Create random input and target
        x = torch.randn(batch_size, *input_dim, requires_grad=True)
        target = torch.randn(batch_size)
        
        # Forward pass
        values = value_net(x)
        loss = torch.nn.MSELoss()(values, target)
        
        # Backward pass
        loss.backward()
        
        # Check that gradients exist
        assert x.grad is not None
        for param in value_net.parameters():
            assert param.grad is not None


class TestNetworkCompatibility:
    """Test compatibility between networks."""
    
    def test_same_input_dimensions(self):
        """Test that policy and value networks work with same input."""
        input_dim = (147,)
        num_actions = 7
        batch_size = 4
        
        policy_net = PolicyNetwork(input_dim, num_actions=num_actions)
        value_net = ValueNetwork(input_dim)
        
        # Create random input
        x = torch.randn(batch_size, *input_dim)
        
        # Forward pass through both networks
        logits = policy_net(x)
        values = value_net(x)
        
        assert logits.shape == (batch_size, num_actions)
        assert values.shape == (batch_size,)
    
    def test_different_hidden_dimensions(self):
        """Test networks with different hidden dimensions."""
        input_dim = (147,)
        num_actions = 7
        
        policy_net = PolicyNetwork(input_dim, hidden_dim=128, num_actions=num_actions)
        value_net = ValueNetwork(input_dim, hidden_dim=256)
        
        # Create random input
        x = torch.randn(1, *input_dim)
        
        # Forward pass
        logits = policy_net(x)
        values = value_net(x)
        
        assert logits.shape == (1, num_actions)
        assert values.shape == (1,)


if __name__ == "__main__":
    pytest.main([__file__])
