#!/usr/bin/env python3
"""
Simple demo script to test the PPO implementation.
"""

import numpy as np
import torch
from llmae_ppo import PPOAgent, PPOTrainer, MiniGridWrapper, Config
from llmae_ppo.utils import set_seed


def test_basic_functionality():
    """Test basic functionality of the PPO implementation."""
    print("Testing LLMaE-PPO Basic Functionality")
    print("=" * 40)
    
    # Set seed for reproducibility
    set_seed(42)
    print("✓ Seed set")
    
    # Test environment creation
    try:
        env = MiniGridWrapper('MiniGrid-Empty-5x5-v0', flatten_obs=True, normalize_obs=True)
        print("✓ Environment created")
        
        # Test environment reset and step
        obs, info = env.reset()
        print(f"✓ Environment reset - observation shape: {obs.shape}")
        
        action = env.action_space.sample()
        next_obs, reward, terminated, truncated, step_info = env.step(action)
        print(f"✓ Environment step - reward: {reward}")
        
    except Exception as e:
        print(f"✗ Environment test failed: {e}")
        return False
    
    # Test agent creation
    try:
        state_dim = env.observation_space.shape
        action_dim = env.action_space.n
        
        agent = PPOAgent(state_dim, action_dim, device='cpu')
        print(f"✓ Agent created - state_dim: {state_dim}, action_dim: {action_dim}")
        
        # Test action selection
        action, log_prob = agent.get_action(obs)
        print(f"✓ Action selected: {action}, log_prob: {log_prob:.4f}")
        
        # Test value estimation
        value = agent.get_value(obs)
        print(f"✓ Value estimated: {value:.4f}")
        
    except Exception as e:
        print(f"✗ Agent test failed: {e}")
        return False
    
    # Test trainer creation
    try:
        trainer = PPOTrainer(agent)
        print("✓ Trainer created")
        
    except Exception as e:
        print(f"✗ Trainer test failed: {e}")
        return False
    
    # Test configuration system
    try:
        config = Config(
            env_name='MiniGrid-Empty-5x5-v0',
            total_timesteps=1000,
            learning_rate=3e-4,
            device='cpu'
        )
        print("✓ Configuration created")
        
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False
    
    env.close()
    print("\n✓ All basic functionality tests passed!")
    return True


def test_mini_training():
    """Test a mini training loop."""
    print("\nTesting Mini Training Loop")
    print("=" * 30)
    
    try:
        # Set seed
        set_seed(42)
        
        # Create environment and agent
        env = MiniGridWrapper('MiniGrid-Empty-5x5-v0', max_steps=20)
        state_dim = env.observation_space.shape
        action_dim = env.action_space.n
        
        agent = PPOAgent(state_dim, action_dim, device='cpu')
        trainer = PPOTrainer(agent)
        
        print("✓ Environment and agent created")
        
        # Collect a small trajectory
        n_steps = 15
        states = []
        actions = []
        log_probs = []
        rewards = []
        dones = []
        
        state, _ = env.reset()
        
        for step in range(n_steps):
            action, log_prob = agent.get_action(state)
            next_state, reward, terminated, truncated, _ = env.step(action)
            done = terminated or truncated
            
            states.append(state)
            actions.append(action)
            log_probs.append(log_prob)
            rewards.append(reward)
            dones.append(done)
            
            if done:
                state, _ = env.reset()
            else:
                state = next_state
        
        print(f"✓ Trajectory collected - {len(states)} steps")
        print(f"  Total reward: {sum(rewards):.3f}")
        print(f"  Episodes completed: {sum(dones)}")
        
        # Update agent
        metrics = trainer.update(states, actions, log_probs, rewards, dones, epochs=1, batch_size=8)
        
        print("✓ Agent updated")
        print(f"  Policy loss: {metrics['policy_loss']:.4f}")
        print(f"  Value loss: {metrics['value_loss']:.4f}")
        print(f"  Entropy loss: {metrics['entropy_loss']:.4f}")
        
        env.close()
        print("\n✓ Mini training test passed!")
        return True
        
    except Exception as e:
        print(f"✗ Mini training test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_network_architectures():
    """Test different network architectures."""
    print("\nTesting Network Architectures")
    print("=" * 32)
    
    try:
        from llmae_ppo.networks import PolicyNetwork, ValueNetwork
        
        # Test flattened input
        input_dim = (147,)
        policy_net = PolicyNetwork(input_dim, num_actions=7)
        value_net = ValueNetwork(input_dim)
        
        x = torch.randn(4, *input_dim)
        logits = policy_net(x)
        values = value_net(x)
        
        print(f"✓ Flattened networks - logits: {logits.shape}, values: {values.shape}")
        
        # Test image input
        input_dim = (3, 7, 7)
        policy_net = PolicyNetwork(input_dim, num_actions=7)
        value_net = ValueNetwork(input_dim)
        
        x = torch.randn(4, *input_dim)
        logits = policy_net(x)
        values = value_net(x)
        
        print(f"✓ Image networks - logits: {logits.shape}, values: {values.shape}")
        
        print("\n✓ Network architecture tests passed!")
        return True
        
    except Exception as e:
        print(f"✗ Network architecture test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main demo function."""
    print("LLMaE-PPO Demo")
    print("=" * 50)
    print("Testing the PPO implementation for MiniGrid environments")
    print()
    
    # Check PyTorch
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")
    print()
    
    # Run tests
    tests = [
        test_basic_functionality,
        test_network_architectures,
        test_mini_training,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    # Summary
    print("=" * 50)
    print(f"Demo Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The PPO implementation is working correctly.")
        print("\nNext steps:")
        print("1. Run full training: python main.py --config configs/quick_test.yaml")
        print("2. Run tests: python run_tests.py")
        print("3. Try different environments: python main.py --env-name MiniGrid-DoorKey-6x6-v0")
    else:
        print("❌ Some tests failed. Please check the implementation.")
    
    return passed == total


if __name__ == "__main__":
    main()
