"""
Evaluation script for trained PPO agents on MiniGrid environments.
"""

import argparse
import numpy as np
import torch
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import json

from llmae_ppo import PPOAgent, MiniGridWrapper, Config
from llmae_ppo.utils import set_seed


def evaluate_agent(env, agent, n_episodes=100, render=False, save_video=False):
    """
    Evaluate agent performance over multiple episodes.
    
    Args:
        env: Environment to evaluate on
        agent: Trained PPO agent
        n_episodes: Number of episodes to evaluate
        render: Whether to render episodes
        save_video: Whether to save video of episodes
    
    Returns:
        Dictionary with evaluation metrics
    """
    episode_rewards = []
    episode_lengths = []
    success_rate = 0
    
    for episode in range(n_episodes):
        state, _ = env.reset()
        episode_reward = 0
        episode_length = 0
        done = False
        
        while not done:
            if render:
                env.render()
            
            action, _ = agent.get_action(state)
            state, reward, terminated, truncated, info = env.step(action)
            done = terminated or truncated
            episode_reward += reward
            episode_length += 1
        
        episode_rewards.append(episode_reward)
        episode_lengths.append(episode_length)
        
        # Check if episode was successful (reached goal)
        if episode_reward > 0:
            success_rate += 1
        
        if (episode + 1) % 10 == 0:
            print(f"Episode {episode + 1}/{n_episodes} completed")
    
    success_rate = success_rate / n_episodes
    
    return {
        'episode_rewards': episode_rewards,
        'episode_lengths': episode_lengths,
        'mean_reward': np.mean(episode_rewards),
        'std_reward': np.std(episode_rewards),
        'min_reward': np.min(episode_rewards),
        'max_reward': np.max(episode_rewards),
        'mean_length': np.mean(episode_lengths),
        'std_length': np.std(episode_lengths),
        'min_length': np.min(episode_lengths),
        'max_length': np.max(episode_lengths),
        'success_rate': success_rate
    }


def plot_evaluation_results(results, save_path=None):
    """
    Create visualization plots for evaluation results.
    
    Args:
        results: Dictionary with evaluation metrics
        save_path: Path to save plots (optional)
    """
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    fig.suptitle('Agent Evaluation Results', fontsize=16)
    
    # Episode rewards histogram
    axes[0, 0].hist(results['episode_rewards'], bins=20, alpha=0.7, color='blue')
    axes[0, 0].axvline(results['mean_reward'], color='red', linestyle='--', 
                      label=f'Mean: {results["mean_reward"]:.2f}')
    axes[0, 0].set_xlabel('Episode Reward')
    axes[0, 0].set_ylabel('Frequency')
    axes[0, 0].set_title('Distribution of Episode Rewards')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # Episode lengths histogram
    axes[0, 1].hist(results['episode_lengths'], bins=20, alpha=0.7, color='green')
    axes[0, 1].axvline(results['mean_length'], color='red', linestyle='--',
                      label=f'Mean: {results["mean_length"]:.2f}')
    axes[0, 1].set_xlabel('Episode Length')
    axes[0, 1].set_ylabel('Frequency')
    axes[0, 1].set_title('Distribution of Episode Lengths')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # Episode rewards over time
    axes[1, 0].plot(results['episode_rewards'], alpha=0.7, color='blue')
    axes[1, 0].axhline(results['mean_reward'], color='red', linestyle='--',
                      label=f'Mean: {results["mean_reward"]:.2f}')
    axes[1, 0].set_xlabel('Episode')
    axes[1, 0].set_ylabel('Reward')
    axes[1, 0].set_title('Episode Rewards Over Time')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # Episode lengths over time
    axes[1, 1].plot(results['episode_lengths'], alpha=0.7, color='green')
    axes[1, 1].axhline(results['mean_length'], color='red', linestyle='--',
                      label=f'Mean: {results["mean_length"]:.2f}')
    axes[1, 1].set_xlabel('Episode')
    axes[1, 1].set_ylabel('Length')
    axes[1, 1].set_title('Episode Lengths Over Time')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Evaluation plots saved to {save_path}")
    
    plt.show()


def print_evaluation_summary(results):
    """Print a summary of evaluation results."""
    print("\n" + "="*50)
    print("EVALUATION SUMMARY")
    print("="*50)
    print(f"Episodes evaluated: {len(results['episode_rewards'])}")
    print(f"Success rate: {results['success_rate']:.2%}")
    print()
    print("REWARDS:")
    print(f"  Mean: {results['mean_reward']:.3f} ± {results['std_reward']:.3f}")
    print(f"  Min:  {results['min_reward']:.3f}")
    print(f"  Max:  {results['max_reward']:.3f}")
    print()
    print("EPISODE LENGTHS:")
    print(f"  Mean: {results['mean_length']:.1f} ± {results['std_length']:.1f}")
    print(f"  Min:  {results['min_length']}")
    print(f"  Max:  {results['max_length']}")
    print("="*50)


def compare_agents(agent_paths, env_name, n_episodes=50):
    """
    Compare multiple trained agents.
    
    Args:
        agent_paths: List of paths to trained agent checkpoints
        env_name: Environment name to evaluate on
        n_episodes: Number of episodes per agent
    """
    results = {}
    
    # Create environment
    env = MiniGridWrapper(env_name, flatten_obs=True, normalize_obs=True)
    state_dim = env.observation_space.shape
    action_dim = env.action_space.n
    
    for i, agent_path in enumerate(agent_paths):
        print(f"\nEvaluating agent {i+1}/{len(agent_paths)}: {agent_path}")
        
        # Load agent
        agent = PPOAgent(state_dim, action_dim, device='cpu')
        agent.load(agent_path)
        
        # Evaluate
        result = evaluate_agent(env, agent, n_episodes)
        results[f'Agent_{i+1}'] = result
        
        print(f"Mean reward: {result['mean_reward']:.3f} ± {result['std_reward']:.3f}")
        print(f"Success rate: {result['success_rate']:.2%}")
    
    env.close()
    
    # Create comparison plot
    fig, axes = plt.subplots(1, 2, figsize=(12, 5))
    
    agent_names = list(results.keys())
    mean_rewards = [results[name]['mean_reward'] for name in agent_names]
    std_rewards = [results[name]['std_reward'] for name in agent_names]
    success_rates = [results[name]['success_rate'] for name in agent_names]
    
    # Mean rewards comparison
    axes[0].bar(agent_names, mean_rewards, yerr=std_rewards, capsize=5, alpha=0.7)
    axes[0].set_ylabel('Mean Episode Reward')
    axes[0].set_title('Agent Performance Comparison')
    axes[0].grid(True, alpha=0.3)
    
    # Success rates comparison
    axes[1].bar(agent_names, success_rates, alpha=0.7, color='green')
    axes[1].set_ylabel('Success Rate')
    axes[1].set_title('Success Rate Comparison')
    axes[1].set_ylim(0, 1)
    axes[1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    return results


def main():
    """Main evaluation function."""
    parser = argparse.ArgumentParser(description='Evaluate trained PPO agent')
    parser.add_argument('--model-path', type=str, required=True,
                       help='Path to trained model checkpoint')
    parser.add_argument('--env-name', type=str, default='MiniGrid-Empty-8x8-v0',
                       help='MiniGrid environment name')
    parser.add_argument('--n-episodes', type=int, default=100,
                       help='Number of episodes to evaluate')
    parser.add_argument('--render', action='store_true',
                       help='Render episodes during evaluation')
    parser.add_argument('--save-plots', type=str,
                       help='Path to save evaluation plots')
    parser.add_argument('--save-results', type=str,
                       help='Path to save evaluation results (JSON)')
    parser.add_argument('--seed', type=int, default=42,
                       help='Random seed for evaluation')
    parser.add_argument('--compare', nargs='+',
                       help='Compare multiple agents (provide multiple model paths)')
    
    args = parser.parse_args()
    
    # Set seed
    set_seed(args.seed)
    
    if args.compare:
        # Compare multiple agents
        results = compare_agents(args.compare, args.env_name, args.n_episodes)
    else:
        # Evaluate single agent
        print(f"Evaluating agent: {args.model_path}")
        print(f"Environment: {args.env_name}")
        print(f"Episodes: {args.n_episodes}")
        
        # Create environment
        env = MiniGridWrapper(args.env_name, flatten_obs=True, normalize_obs=True)
        state_dim = env.observation_space.shape
        action_dim = env.action_space.n
        
        # Load agent
        agent = PPOAgent(state_dim, action_dim, device='cpu')
        agent.load(args.model_path)
        
        # Evaluate
        results = evaluate_agent(env, agent, args.n_episodes, args.render)
        
        # Print summary
        print_evaluation_summary(results)
        
        # Create plots
        plot_evaluation_results(results, args.save_plots)
        
        # Save results
        if args.save_results:
            # Remove numpy arrays for JSON serialization
            json_results = {k: v for k, v in results.items() 
                          if k not in ['episode_rewards', 'episode_lengths']}
            json_results['n_episodes'] = len(results['episode_rewards'])
            
            with open(args.save_results, 'w') as f:
                json.dump(json_results, f, indent=2)
            print(f"Results saved to {args.save_results}")
        
        env.close()


if __name__ == "__main__":
    main()
