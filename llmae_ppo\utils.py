"""
Utility functions and classes for PPO training.
"""

import os
import json
import yaml
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import torch
from collections import deque
import logging


class Config:
    """Configuration management class."""
    
    def __init__(self, config_path=None, **kwargs):
        """
        Initialize configuration.
        
        Args:
            config_path: Path to configuration file (JSON or YAML)
            **kwargs: Additional configuration parameters
        """
        # Default configuration
        self.defaults = {
            # Environment settings
            'env_name': 'MiniGrid-Empty-8x8-v0',
            'flatten_obs': True,
            'normalize_obs': True,
            'max_episode_steps': 1000,
            
            # Training settings
            'total_timesteps': 1000000,
            'learning_rate': 3e-4,
            'batch_size': 64,
            'n_epochs': 4,
            'n_steps': 2048,
            'gamma': 0.99,
            'gae_lambda': 0.95,
            'clip_epsilon': 0.2,
            'value_coef': 0.5,
            'entropy_coef': 0.01,
            'max_grad_norm': 0.5,
            
            # Network settings
            'hidden_dim': 256,
            
            # Logging and saving
            'log_interval': 10,
            'save_interval': 100,
            'eval_interval': 50,
            'eval_episodes': 10,
            'save_dir': 'checkpoints',
            'log_dir': 'logs',
            'use_wandb': False,
            'wandb_project': 'llmae-ppo',
            
            # Device settings
            'device': 'cuda' if torch.cuda.is_available() else 'cpu',
            'seed': 42
        }
        
        # Load from file if provided
        if config_path:
            self.load_from_file(config_path)
        
        # Update with kwargs
        for key, value in kwargs.items():
            setattr(self, key, value)
        
        # Set defaults for missing attributes
        for key, value in self.defaults.items():
            if not hasattr(self, key):
                setattr(self, key, value)
    
    def load_from_file(self, config_path):
        """Load configuration from file."""
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"Configuration file not found: {config_path}")
        
        with open(config_path, 'r') as f:
            if config_path.endswith('.json'):
                config_dict = json.load(f)
            elif config_path.endswith(('.yaml', '.yml')):
                config_dict = yaml.safe_load(f)
            else:
                raise ValueError("Configuration file must be JSON or YAML")
        
        for key, value in config_dict.items():
            setattr(self, key, value)
    
    def save_to_file(self, config_path):
        """Save configuration to file."""
        config_dict = {key: getattr(self, key) for key in self.defaults.keys()}
        
        os.makedirs(os.path.dirname(config_path), exist_ok=True)
        
        with open(config_path, 'w') as f:
            if config_path.endswith('.json'):
                json.dump(config_dict, f, indent=2)
            elif config_path.endswith(('.yaml', '.yml')):
                yaml.dump(config_dict, f, default_flow_style=False)
            else:
                raise ValueError("Configuration file must be JSON or YAML")
    
    def __str__(self):
        """String representation of configuration."""
        config_str = "Configuration:\n"
        for key in sorted(self.defaults.keys()):
            config_str += f"  {key}: {getattr(self, key)}\n"
        return config_str


class Logger:
    """Logging utility for training metrics."""
    
    def __init__(self, log_dir='logs', use_wandb=False, wandb_project='llmae-ppo'):
        """
        Initialize logger.
        
        Args:
            log_dir: Directory for log files
            use_wandb: Whether to use Weights & Biases
            wandb_project: W&B project name
        """
        self.log_dir = log_dir
        self.use_wandb = use_wandb
        
        # Create log directory
        os.makedirs(log_dir, exist_ok=True)
        
        # Setup file logging
        log_file = os.path.join(log_dir, f'training_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # Setup W&B if requested
        if use_wandb:
            try:
                import wandb
                wandb.init(project=wandb_project)
                self.wandb = wandb
            except ImportError:
                self.logger.warning("wandb not installed, disabling W&B logging")
                self.use_wandb = False
        
        # Metrics storage
        self.metrics = {}
        self.episode_rewards = deque(maxlen=100)
        self.episode_lengths = deque(maxlen=100)
    
    def log_scalar(self, name, value, step):
        """Log a scalar value."""
        if name not in self.metrics:
            self.metrics[name] = []
        
        self.metrics[name].append((step, value))
        
        if self.use_wandb:
            self.wandb.log({name: value}, step=step)
    
    def log_episode(self, reward, length, step):
        """Log episode metrics."""
        self.episode_rewards.append(reward)
        self.episode_lengths.append(length)
        
        self.log_scalar('episode_reward', reward, step)
        self.log_scalar('episode_length', length, step)
        
        if len(self.episode_rewards) >= 10:
            mean_reward = np.mean(self.episode_rewards)
            mean_length = np.mean(self.episode_lengths)
            
            self.log_scalar('mean_episode_reward', mean_reward, step)
            self.log_scalar('mean_episode_length', mean_length, step)
            
            self.logger.info(f"Step {step}: Mean reward: {mean_reward:.2f}, Mean length: {mean_length:.2f}")
    
    def log_training_metrics(self, metrics, step):
        """Log training metrics."""
        for key, value in metrics.items():
            self.log_scalar(f'training/{key}', value, step)
    
    def save_plots(self):
        """Save training plots."""
        if not self.metrics:
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(12, 8))
        fig.suptitle('Training Metrics')
        
        # Episode rewards
        if 'episode_reward' in self.metrics:
            steps, rewards = zip(*self.metrics['episode_reward'])
            axes[0, 0].plot(steps, rewards, alpha=0.3)
            if 'mean_episode_reward' in self.metrics:
                steps, mean_rewards = zip(*self.metrics['mean_episode_reward'])
                axes[0, 0].plot(steps, mean_rewards, 'r-', linewidth=2)
            axes[0, 0].set_title('Episode Rewards')
            axes[0, 0].set_xlabel('Steps')
            axes[0, 0].set_ylabel('Reward')
        
        # Episode lengths
        if 'episode_length' in self.metrics:
            steps, lengths = zip(*self.metrics['episode_length'])
            axes[0, 1].plot(steps, lengths, alpha=0.3)
            if 'mean_episode_length' in self.metrics:
                steps, mean_lengths = zip(*self.metrics['mean_episode_length'])
                axes[0, 1].plot(steps, mean_lengths, 'r-', linewidth=2)
            axes[0, 1].set_title('Episode Lengths')
            axes[0, 1].set_xlabel('Steps')
            axes[0, 1].set_ylabel('Length')
        
        # Policy loss
        if 'training/policy_loss' in self.metrics:
            steps, losses = zip(*self.metrics['training/policy_loss'])
            axes[1, 0].plot(steps, losses)
            axes[1, 0].set_title('Policy Loss')
            axes[1, 0].set_xlabel('Steps')
            axes[1, 0].set_ylabel('Loss')
        
        # Value loss
        if 'training/value_loss' in self.metrics:
            steps, losses = zip(*self.metrics['training/value_loss'])
            axes[1, 1].plot(steps, losses)
            axes[1, 1].set_title('Value Loss')
            axes[1, 1].set_xlabel('Steps')
            axes[1, 1].set_ylabel('Loss')
        
        plt.tight_layout()
        plot_path = os.path.join(self.log_dir, 'training_plots.png')
        plt.savefig(plot_path)
        plt.close()
        
        self.logger.info(f"Training plots saved to {plot_path}")


def set_seed(seed):
    """Set random seed for reproducibility."""
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)


def create_directories(config):
    """Create necessary directories."""
    os.makedirs(config.save_dir, exist_ok=True)
    os.makedirs(config.log_dir, exist_ok=True)


def format_time(seconds):
    """Format time in seconds to human readable format."""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    seconds = int(seconds % 60)
    return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
