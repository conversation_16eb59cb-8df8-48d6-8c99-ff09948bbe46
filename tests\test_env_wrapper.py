"""
Unit tests for environment wrapper.
"""

import pytest
import numpy as np
import gymnasium as gym
from llmae_ppo.env_wrapper import MiniGridWrapper, MultiEnvWrapper, make_env, get_available_envs


class TestMiniGridWrapper:
    """Test cases for MiniGridWrapper."""
    
    def test_wrapper_creation_default(self):
        """Test wrapper creation with default settings."""
        env = MiniGridWrapper()
        
        assert env is not None
        assert env.flatten_obs == True
        assert env.normalize_obs == True
        assert hasattr(env, 'observation_space')
        assert hasattr(env, 'action_space')
        
        env.close()
    
    def test_wrapper_creation_custom(self):
        """Test wrapper creation with custom settings."""
        env = MiniGridWrapper(
            env_name='MiniGrid-Empty-5x5-v0',
            flatten_obs=False,
            normalize_obs=False,
            max_steps=100
        )
        
        assert env is not None
        assert env.flatten_obs == False
        assert env.normalize_obs == False
        assert env.max_steps == 100
        
        env.close()
    
    def test_observation_space_flattened(self):
        """Test observation space with flattened observations."""
        env = MiniGridWrapper(flatten_obs=True, normalize_obs=True)
        
        obs_space = env.observation_space
        assert len(obs_space.shape) == 1  # Should be 1D
        assert obs_space.low.min() == 0.0
        assert obs_space.high.max() == 1.0
        assert obs_space.dtype == np.float32
        
        env.close()
    
    def test_observation_space_image(self):
        """Test observation space with image observations."""
        env = MiniGridWrapper(flatten_obs=False, normalize_obs=True)
        
        obs_space = env.observation_space
        assert len(obs_space.shape) == 3  # Should be 3D (C, H, W)
        assert obs_space.low.min() == 0.0
        assert obs_space.high.max() == 1.0
        assert obs_space.dtype == np.float32
        
        env.close()
    
    def test_reset(self):
        """Test environment reset."""
        env = MiniGridWrapper()
        
        obs, info = env.reset()
        
        assert obs is not None
        assert isinstance(obs, np.ndarray)
        assert obs.shape == env.observation_space.shape
        assert obs.dtype == env.observation_space.dtype
        assert isinstance(info, dict)
        
        env.close()
    
    def test_step(self):
        """Test environment step."""
        env = MiniGridWrapper()
        
        obs, info = env.reset()
        action = env.action_space.sample()
        
        next_obs, reward, terminated, truncated, step_info = env.step(action)
        
        assert next_obs is not None
        assert isinstance(next_obs, np.ndarray)
        assert next_obs.shape == env.observation_space.shape
        assert isinstance(reward, (int, float))
        assert isinstance(terminated, bool)
        assert isinstance(truncated, bool)
        assert isinstance(step_info, dict)
        
        env.close()
    
    def test_observation_processing_flattened_normalized(self):
        """Test observation processing with flattening and normalization."""
        env = MiniGridWrapper(flatten_obs=True, normalize_obs=True)
        
        obs, _ = env.reset()
        
        assert obs.ndim == 1  # Should be flattened
        assert obs.min() >= 0.0  # Should be normalized
        assert obs.max() <= 1.0  # Should be normalized
        assert obs.dtype == np.float32
        
        env.close()
    
    def test_observation_processing_image_normalized(self):
        """Test observation processing with image format and normalization."""
        env = MiniGridWrapper(flatten_obs=False, normalize_obs=True)
        
        obs, _ = env.reset()
        
        assert obs.ndim == 3  # Should be image format
        assert obs.min() >= 0.0  # Should be normalized
        assert obs.max() <= 1.0  # Should be normalized
        assert obs.dtype == np.float32
        
        env.close()
    
    def test_max_steps_truncation(self):
        """Test that episodes are truncated at max steps."""
        max_steps = 10
        env = MiniGridWrapper(max_steps=max_steps)
        
        obs, _ = env.reset()
        
        for step in range(max_steps + 5):  # Go beyond max steps
            action = env.action_space.sample()
            obs, reward, terminated, truncated, info = env.step(action)
            
            if terminated or truncated:
                break
        
        # Should have been truncated by max_steps
        assert step < max_steps + 5  # Should have ended before we reached the limit
        
        env.close()
    
    def test_episode_completion(self):
        """Test complete episode run."""
        env = MiniGridWrapper()
        
        obs, _ = env.reset()
        done = False
        step_count = 0
        
        while not done and step_count < 1000:  # Safety limit
            action = env.action_space.sample()
            obs, reward, terminated, truncated, info = env.step(action)
            done = terminated or truncated
            step_count += 1
        
        assert step_count > 0
        assert step_count < 1000  # Should have finished
        
        env.close()


class TestMultiEnvWrapper:
    """Test cases for MultiEnvWrapper."""
    
    def test_multi_env_creation(self):
        """Test multi-environment wrapper creation."""
        num_envs = 4
        multi_env = MultiEnvWrapper(num_envs=num_envs)
        
        assert multi_env is not None
        assert multi_env.num_envs == num_envs
        assert len(multi_env.envs) == num_envs
        assert hasattr(multi_env, 'observation_space')
        assert hasattr(multi_env, 'action_space')
        
        multi_env.close()
    
    def test_multi_env_reset(self):
        """Test multi-environment reset."""
        num_envs = 3
        multi_env = MultiEnvWrapper(num_envs=num_envs)
        
        observations, infos = multi_env.reset()
        
        assert observations.shape[0] == num_envs
        assert len(infos) == num_envs
        assert all(isinstance(info, dict) for info in infos)
        
        multi_env.close()
    
    def test_multi_env_step(self):
        """Test multi-environment step."""
        num_envs = 3
        multi_env = MultiEnvWrapper(num_envs=num_envs)
        
        observations, infos = multi_env.reset()
        actions = [multi_env.action_space.sample() for _ in range(num_envs)]
        
        next_obs, rewards, terminateds, truncateds, step_infos = multi_env.step(actions)
        
        assert next_obs.shape[0] == num_envs
        assert len(rewards) == num_envs
        assert len(terminateds) == num_envs
        assert len(truncateds) == num_envs
        assert len(step_infos) == num_envs
        
        multi_env.close()
    
    def test_multi_env_auto_reset(self):
        """Test that environments auto-reset when done."""
        num_envs = 2
        multi_env = MultiEnvWrapper(num_envs=num_envs, max_steps=10)
        
        observations, _ = multi_env.reset()
        
        # Run for many steps to ensure some episodes finish
        for _ in range(50):
            actions = [multi_env.action_space.sample() for _ in range(num_envs)]
            observations, rewards, terminateds, truncateds, infos = multi_env.step(actions)
            
            # Check that observations are still valid (auto-reset worked)
            assert observations.shape[0] == num_envs
            assert not np.isnan(observations).any()
        
        multi_env.close()


class TestUtilityFunctions:
    """Test utility functions."""
    
    def test_make_env(self):
        """Test make_env factory function."""
        env = make_env('MiniGrid-Empty-5x5-v0', flatten_obs=True, normalize_obs=True)
        
        assert isinstance(env, MiniGridWrapper)
        assert env.flatten_obs == True
        assert env.normalize_obs == True
        
        env.close()
    
    def test_get_available_envs(self):
        """Test getting available environments."""
        available_envs = get_available_envs()
        
        assert isinstance(available_envs, list)
        assert len(available_envs) > 0
        assert all(isinstance(env_name, str) for env_name in available_envs)
        assert all(env_name.startswith('MiniGrid-') for env_name in available_envs)
        
        # Test that at least some common environments are available
        common_envs = ['MiniGrid-Empty-5x5-v0', 'MiniGrid-Empty-8x8-v0']
        for env_name in common_envs:
            if env_name in available_envs:
                # Test that we can actually create this environment
                env = make_env(env_name)
                env.close()
                break
        else:
            pytest.skip("No common test environments available")


class TestEnvironmentCompatibility:
    """Test compatibility with different MiniGrid environments."""
    
    @pytest.mark.parametrize("env_name", [
        'MiniGrid-Empty-5x5-v0',
        'MiniGrid-Empty-8x8-v0',
    ])
    def test_different_environments(self, env_name):
        """Test wrapper with different MiniGrid environments."""
        try:
            env = MiniGridWrapper(env_name=env_name)
            
            # Test basic functionality
            obs, _ = env.reset()
            assert obs is not None
            
            action = env.action_space.sample()
            next_obs, reward, terminated, truncated, info = env.step(action)
            assert next_obs is not None
            
            env.close()
            
        except gym.error.UnregisteredEnv:
            pytest.skip(f"Environment {env_name} not available")
    
    def test_observation_consistency(self):
        """Test that observations are consistent across resets."""
        env = MiniGridWrapper()
        
        # Get multiple observations
        obs1, _ = env.reset(seed=42)
        obs2, _ = env.reset(seed=42)
        
        # Should be identical with same seed
        assert np.array_equal(obs1, obs2)
        
        # Should be different with different seeds
        obs3, _ = env.reset(seed=123)
        assert not np.array_equal(obs1, obs3)
        
        env.close()


if __name__ == "__main__":
    pytest.main([__file__])
