"""
Main training script for PPO on MiniGrid environments.
"""

import argparse
import time
import numpy as np
import torch
from collections import deque

from llmae_ppo import PPOAgent, <PERSON><PERSON><PERSON><PERSON>, MiniGridWrapper, Config, Logger
from llmae_ppo.utils import set_seed, create_directories, format_time


def collect_trajectories(env, agent, n_steps):
    """Collect trajectories from environment."""
    states = []
    actions = []
    log_probs = []
    rewards = []
    dones = []

    state, _ = env.reset()

    for _ in range(n_steps):
        action, log_prob = agent.get_action(state)
        next_state, reward, terminated, truncated, _ = env.step(action)
        done = terminated or truncated

        states.append(state)
        actions.append(action)
        log_probs.append(log_prob)
        rewards.append(reward)
        dones.append(done)

        if done:
            state, _ = env.reset()
        else:
            state = next_state

    return states, actions, log_probs, rewards, dones


def evaluate_agent(env, agent, n_episodes=10):
    """Evaluate agent performance."""
    episode_rewards = []
    episode_lengths = []

    for _ in range(n_episodes):
        state, _ = env.reset()
        episode_reward = 0
        episode_length = 0
        done = False

        while not done:
            action, _ = agent.get_action(state)
            state, reward, terminated, truncated, _ = env.step(action)
            done = terminated or truncated
            episode_reward += reward
            episode_length += 1

        episode_rewards.append(episode_reward)
        episode_lengths.append(episode_length)

    return {
        'mean_reward': np.mean(episode_rewards),
        'std_reward': np.std(episode_rewards),
        'mean_length': np.mean(episode_lengths),
        'std_length': np.std(episode_lengths)
    }


def train_ppo(config):
    """Main training function."""
    # Set seed for reproducibility
    set_seed(config.seed)

    # Create directories
    create_directories(config)

    # Initialize logger
    logger = Logger(config.log_dir, config.use_wandb, config.wandb_project)
    logger.logger.info("Starting PPO training")
    logger.logger.info(str(config))

    # Create environment
    env = MiniGridWrapper(
        config.env_name,
        flatten_obs=config.flatten_obs,
        normalize_obs=config.normalize_obs,
        max_steps=config.max_episode_steps
    )

    # Get environment dimensions
    state_dim = env.observation_space.shape
    action_dim = env.action_space.n

    logger.logger.info(f"Environment: {config.env_name}")
    logger.logger.info(f"State dimension: {state_dim}")
    logger.logger.info(f"Action dimension: {action_dim}")

    # Initialize agent and trainer
    agent = PPOAgent(state_dim, action_dim, config.learning_rate, config.device)
    trainer = PPOTrainer(
        agent,
        gamma=config.gamma,
        gae_lambda=config.gae_lambda,
        clip_epsilon=config.clip_epsilon,
        value_coef=config.value_coef,
        entropy_coef=config.entropy_coef,
        max_grad_norm=config.max_grad_norm
    )

    # Training loop
    total_steps = 0
    episode_count = 0
    start_time = time.time()

    while total_steps < config.total_timesteps:
        # Collect trajectories
        states, actions, log_probs, rewards, dones = collect_trajectories(
            env, agent, config.n_steps
        )

        # Update agent
        training_metrics = trainer.update(
            states, actions, log_probs, rewards, dones,
            epochs=config.n_epochs, batch_size=config.batch_size
        )

        total_steps += config.n_steps

        # Log training metrics
        logger.log_training_metrics(training_metrics, total_steps)

        # Log episode metrics
        episode_rewards = []
        episode_lengths = []
        current_reward = 0
        current_length = 0

        for i, (reward, done) in enumerate(zip(rewards, dones)):
            current_reward += reward
            current_length += 1

            if done:
                episode_rewards.append(current_reward)
                episode_lengths.append(current_length)
                current_reward = 0
                current_length = 0
                episode_count += 1

        # Log episodes
        for reward, length in zip(episode_rewards, episode_lengths):
            logger.log_episode(reward, length, total_steps)

        # Periodic logging
        if total_steps % (config.log_interval * config.n_steps) == 0:
            elapsed_time = time.time() - start_time
            steps_per_sec = total_steps / elapsed_time

            logger.logger.info(
                f"Steps: {total_steps}/{config.total_timesteps} "
                f"({100 * total_steps / config.total_timesteps:.1f}%) | "
                f"Episodes: {episode_count} | "
                f"Time: {format_time(elapsed_time)} | "
                f"Steps/sec: {steps_per_sec:.1f}"
            )

        # Periodic evaluation
        if total_steps % (config.eval_interval * config.n_steps) == 0:
            eval_metrics = evaluate_agent(env, agent, config.eval_episodes)
            logger.logger.info(
                f"Evaluation - Mean reward: {eval_metrics['mean_reward']:.2f} ± "
                f"{eval_metrics['std_reward']:.2f}, "
                f"Mean length: {eval_metrics['mean_length']:.2f} ± "
                f"{eval_metrics['std_length']:.2f}"
            )

            for key, value in eval_metrics.items():
                logger.log_scalar(f'eval/{key}', value, total_steps)

        # Periodic saving
        if total_steps % (config.save_interval * config.n_steps) == 0:
            save_path = f"{config.save_dir}/ppo_checkpoint_{total_steps}.pt"
            agent.save(save_path)
            logger.logger.info(f"Model saved to {save_path}")

    # Final evaluation and saving
    final_eval = evaluate_agent(env, agent, config.eval_episodes)
    logger.logger.info(f"Final evaluation: {final_eval}")

    final_save_path = f"{config.save_dir}/ppo_final.pt"
    agent.save(final_save_path)
    logger.logger.info(f"Final model saved to {final_save_path}")

    # Save training plots
    logger.save_plots()

    # Save configuration
    config.save_to_file(f"{config.log_dir}/config.yaml")

    env.close()
    logger.logger.info("Training completed!")


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Train PPO on MiniGrid')
    parser.add_argument('--config', type=str, help='Path to configuration file')
    parser.add_argument('--env-name', type=str, default='MiniGrid-Empty-8x8-v0',
                       help='MiniGrid environment name')
    parser.add_argument('--total-timesteps', type=int, default=1000000,
                       help='Total training timesteps')
    parser.add_argument('--learning-rate', type=float, default=3e-4,
                       help='Learning rate')
    parser.add_argument('--seed', type=int, default=42, help='Random seed')
    parser.add_argument('--device', type=str, default='auto',
                       help='Device to use (cpu, cuda, auto)')
    parser.add_argument('--use-wandb', action='store_true',
                       help='Use Weights & Biases logging')

    args = parser.parse_args()

    # Create configuration
    config_kwargs = {}
    if args.env_name:
        config_kwargs['env_name'] = args.env_name
    if args.total_timesteps:
        config_kwargs['total_timesteps'] = args.total_timesteps
    if args.learning_rate:
        config_kwargs['learning_rate'] = args.learning_rate
    if args.seed:
        config_kwargs['seed'] = args.seed
    if args.use_wandb:
        config_kwargs['use_wandb'] = args.use_wandb

    if args.device != 'auto':
        config_kwargs['device'] = args.device

    config = Config(args.config, **config_kwargs)

    # Start training
    train_ppo(config)


if __name__ == "__main__":
    main()
