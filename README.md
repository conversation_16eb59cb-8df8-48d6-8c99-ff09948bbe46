# LLM as Expert PPO

**LLM as Expert PPO** is a research project that explores how large language models (LLMs) can be used to initialize policies in reinforcement learning. Specifically, we use an LLM to generate expert-like trajectories from natural language descriptions of a task. These trajectories are used to pretrain a PPO (Proximal Policy Optimization) agent via imitation learning.

By starting PPO training from an LLM-informed prior, we aim to improve convergence speed and sample efficiency—especially in environments where exploration is difficult or reward signals are sparse. This method avoids the need for in-loop LLM calls, making it lightweight and scalable.

## Key Features
- One-time LLM prompt to generate demonstrations
- Behavior cloning for policy pretraining
- PPO training and evaluation in MiniGrid and MinAtar environments
- Baselines: Random initialization and transfer learning
- Evaluation via sample efficiency, return curves, and visitation heatmaps

## Project Scope
This project was developed as part of a university reinforcement learning course. It includes a complete experimental pipeline, including data generation, training, evaluation, and visualization.
