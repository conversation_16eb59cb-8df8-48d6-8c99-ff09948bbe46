# LLMaE-PPO

A minimal PPO implementation for MiniGrid environments with GIF trajectory visualization.

## Quick Start

1. **Install dependencies:**
   ```bash
   uv sync
   ```

2. **Train PPO agent:**
   ```bash
   python main.py --env-name MiniGrid-Empty-8x8-v0 --total-timesteps 100000
   ```

3. **Evaluate and create GIF:**
   ```bash
   python evaluate.py --model-path checkpoints/ppo_final.pt --gif-path agent_trajectory.gif
   ```

## Usage

### Training
```bash
# Basic training
python main.py

# Custom environment and timesteps
python main.py --env-name MiniGrid-DoorKey-6x6-v0 --total-timesteps 200000

# GPU training
python main.py --device cuda
```

### Evaluation with GIF
```bash
# Create GIF and evaluate performance
python evaluate.py --model-path checkpoints/ppo_final.pt --gif-path my_agent.gif

# Different environment
python evaluate.py --model-path checkpoints/ppo_final.pt --env-name MiniGrid-<PERSON>Key-6x6-v0 --gif-path doorkey_agent.gif
```

## Files

- `main.py` - Training script
- `evaluate.py` - Evaluation script with GIF generation
- `llmae_ppo/` - PPO implementation
  - `ppo.py` - PPO agent and trainer
  - `networks.py` - Policy and value networks
  - `env_wrapper.py` - MiniGrid environment wrapper
  - `utils.py` - Utility functions

## Output

- **Training**: Saves checkpoints to `checkpoints/`
- **Evaluation**: Creates GIF showing agent's trajectory and prints performance metrics
