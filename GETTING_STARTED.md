# Getting Started with LLMaE-PPO

This guide will help you get up and running with the LLMaE-PPO implementation quickly.

## Quick Setup

1. **Install uv** (if not already installed):
   ```bash
   curl -LsSf https://astral.sh/uv/install.sh | sh
   ```

2. **<PERSON>lone and setup the project**:
   ```bash
   git clone <your-repo-url>
   cd LLMaE-PPO
   uv sync
   ```

3. **Activate the virtual environment**:
   ```bash
   source .venv/bin/activate  # On Windows: .venv\Scripts\activate
   ```

## Quick Test

Run the demo script to verify everything works:

```bash
python demo.py
```

This will test:
- Environment creation and interaction
- Neural network architectures
- Basic PPO training loop

## Your First Training Run

### Option 1: Quick Test (5 minutes)
```bash
python main.py --config configs/quick_test.yaml
```

This runs a short training session on a simple environment for testing.

### Option 2: Default Training (30-60 minutes)
```bash
python main.py --config configs/default.yaml
```

This runs a full training session with default hyperparameters.

### Option 3: Custom Training
```bash
python main.py --env-name MiniGrid-DoorKey-6x6-v0 --total-timesteps 500000 --device cuda
```

## Monitor Training

Training logs are saved to the `logs/` directory. You can monitor progress by:

1. **Checking the console output** for real-time updates
2. **Viewing log files** in the `logs/` directory
3. **Using TensorBoard** (if configured)
4. **Using Weights & Biases** with `--use-wandb`

## Evaluate Your Trained Agent

After training, evaluate your agent:

```bash
python evaluate.py --model-path checkpoints/ppo_final.pt --n-episodes 100 --save-plots results.png
```

## Common Commands

### Training Commands
```bash
# Quick test
python main.py --config configs/quick_test.yaml

# Different environment
python main.py --env-name MiniGrid-FourRooms-v0

# GPU training
python main.py --device cuda

# With logging
python main.py --use-wandb
```

### Evaluation Commands
```bash
# Basic evaluation
python evaluate.py --model-path checkpoints/ppo_final.pt

# With visualization
python evaluate.py --model-path checkpoints/ppo_final.pt --render

# Compare models
python evaluate.py --compare model1.pt model2.pt model3.pt

# Different environment
python evaluate.py --model-path checkpoints/ppo_final.pt --env-name MiniGrid-DoorKey-8x8-v0
```

### Testing Commands
```bash
# Run all tests
python run_tests.py

# Run specific tests
python run_tests.py networks
python run_tests.py ppo
python run_tests.py env

# Verbose output
python run_tests.py -v

# With coverage
python run_tests.py --coverage
```

## Troubleshooting

### Common Issues

1. **Import errors**: Make sure the virtual environment is activated
2. **CUDA errors**: Use `--device cpu` for CPU-only training
3. **Environment not found**: Check available environments with the demo script
4. **Slow training**: Reduce batch size or use GPU

### Getting Help

1. Check the [README.md](README.md) for detailed documentation
2. Run the demo script to test basic functionality
3. Check the `tests/` directory for usage examples
4. Look at configuration files in `configs/` for parameter examples

## Next Steps

Once you have a working setup:

1. **Experiment with different environments** - Try various MiniGrid tasks
2. **Tune hyperparameters** - Modify configuration files
3. **Extend the implementation** - Add new features or algorithms
4. **Compare performance** - Evaluate on multiple environments

## File Structure Overview

```
LLMaE-PPO/
├── llmae_ppo/           # Main package
├── configs/             # Configuration files
├── tests/               # Unit and integration tests
├── main.py             # Training script
├── evaluate.py         # Evaluation script
├── demo.py             # Demo/test script
├── run_tests.py        # Test runner
└── README.md           # Full documentation
```

## Configuration Tips

- Start with `configs/quick_test.yaml` for testing
- Use `configs/default.yaml` for serious training
- Modify learning rate first when tuning
- Increase `total_timesteps` for better performance
- Use smaller `batch_size` if running out of memory

Happy training! 🚀
