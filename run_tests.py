#!/usr/bin/env python3
"""
Test runner script for LLMaE-PPO.
"""

import sys
import subprocess
import argparse
from pathlib import Path


def run_tests(test_path=None, verbose=False, coverage=False):
    """
    Run tests using pytest.
    
    Args:
        test_path: Specific test file or directory to run
        verbose: Whether to run in verbose mode
        coverage: Whether to run with coverage reporting
    """
    cmd = ["python", "-m", "pytest"]
    
    if test_path:
        cmd.append(str(test_path))
    else:
        cmd.append("tests/")
    
    if verbose:
        cmd.append("-v")
    
    if coverage:
        cmd.extend(["--cov=llmae_ppo", "--cov-report=html", "--cov-report=term"])
    
    # Add other useful pytest options
    cmd.extend([
        "--tb=short",  # Shorter traceback format
        "-x",          # Stop on first failure
        "--strict-markers",  # Strict marker checking
    ])
    
    print(f"Running command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True)
        return result.returncode
    except subprocess.CalledProcessError as e:
        print(f"Tests failed with return code: {e.returncode}")
        return e.returncode
    except FileNotFoundError:
        print("Error: pytest not found. Please install it with: uv add pytest --dev")
        return 1


def run_specific_tests():
    """Run specific test categories."""
    test_categories = {
        "networks": "tests/test_networks.py",
        "ppo": "tests/test_ppo.py", 
        "env": "tests/test_env_wrapper.py",
        "all": "tests/"
    }
    
    print("Available test categories:")
    for category, path in test_categories.items():
        print(f"  {category}: {path}")
    
    return test_categories


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Run tests for LLMaE-PPO")
    parser.add_argument(
        "test_category", 
        nargs="?", 
        default="all",
        help="Test category to run (networks, ppo, env, all)"
    )
    parser.add_argument(
        "-v", "--verbose", 
        action="store_true",
        help="Run tests in verbose mode"
    )
    parser.add_argument(
        "--coverage", 
        action="store_true",
        help="Run tests with coverage reporting"
    )
    parser.add_argument(
        "--list", 
        action="store_true",
        help="List available test categories"
    )
    parser.add_argument(
        "--path",
        type=str,
        help="Specific test file or directory path"
    )
    
    args = parser.parse_args()
    
    test_categories = run_specific_tests()
    
    if args.list:
        return 0
    
    # Determine test path
    if args.path:
        test_path = Path(args.path)
        if not test_path.exists():
            print(f"Error: Test path '{test_path}' does not exist")
            return 1
    elif args.test_category in test_categories:
        test_path = Path(test_categories[args.test_category])
    else:
        print(f"Error: Unknown test category '{args.test_category}'")
        print("Use --list to see available categories")
        return 1
    
    # Run tests
    return run_tests(test_path, args.verbose, args.coverage)


if __name__ == "__main__":
    sys.exit(main())
