"""
Integration tests for the complete PPO system.
"""

import pytest
import tempfile
import shutil
import numpy as np
from pathlib import Path

from llmae_ppo import PPOAgent, PP<PERSON>rainer, MiniGridWrapper, Config
from llmae_ppo.utils import set_seed


class TestSystemIntegration:
    """Integration tests for the complete system."""
    
    def test_basic_training_loop(self):
        """Test a basic training loop with minimal steps."""
        # Set seed for reproducibility
        set_seed(42)
        
        # Create environment
        env = MiniGridWrapper('MiniGrid-Empty-5x5-v0', flatten_obs=True, normalize_obs=True)
        
        # Get environment dimensions
        state_dim = env.observation_space.shape
        action_dim = env.action_space.n
        
        # Create agent and trainer
        agent = PPOAgent(state_dim, action_dim, device='cpu')
        trainer = PPOTrainer(agent)
        
        # Collect a small trajectory
        n_steps = 20
        states = []
        actions = []
        log_probs = []
        rewards = []
        dones = []
        
        state, _ = env.reset()
        
        for _ in range(n_steps):
            action, log_prob = agent.get_action(state)
            next_state, reward, terminated, truncated, _ = env.step(action)
            done = terminated or truncated
            
            states.append(state)
            actions.append(action)
            log_probs.append(log_prob)
            rewards.append(reward)
            dones.append(done)
            
            if done:
                state, _ = env.reset()
            else:
                state = next_state
        
        # Update agent
        metrics = trainer.update(states, actions, log_probs, rewards, dones, epochs=1, batch_size=10)
        
        # Verify training worked
        assert 'policy_loss' in metrics
        assert 'value_loss' in metrics
        assert 'entropy_loss' in metrics
        assert all(isinstance(v, float) for v in metrics.values())
        assert all(not np.isnan(v) for v in metrics.values())
        
        env.close()
    
    def test_config_system(self):
        """Test configuration system."""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_path = Path(temp_dir) / "test_config.yaml"
            
            # Create config
            config = Config(
                env_name='MiniGrid-Empty-5x5-v0',
                total_timesteps=1000,
                learning_rate=1e-4,
                device='cpu'
            )
            
            # Save config
            config.save_to_file(str(config_path))
            
            # Load config
            loaded_config = Config(str(config_path))
            
            # Verify config was loaded correctly
            assert loaded_config.env_name == 'MiniGrid-Empty-5x5-v0'
            assert loaded_config.total_timesteps == 1000
            assert loaded_config.learning_rate == 1e-4
            assert loaded_config.device == 'cpu'
    
    def test_save_and_load_agent(self):
        """Test saving and loading trained agent."""
        with tempfile.TemporaryDirectory() as temp_dir:
            model_path = Path(temp_dir) / "test_model.pt"
            
            # Create and train agent
            env = MiniGridWrapper('MiniGrid-Empty-5x5-v0')
            state_dim = env.observation_space.shape
            action_dim = env.action_space.n
            
            agent1 = PPOAgent(state_dim, action_dim, device='cpu')
            trainer = PPOTrainer(agent1)
            
            # Do a small training update
            states = [np.random.randn(*state_dim) for _ in range(10)]
            actions = [np.random.randint(0, action_dim) for _ in range(10)]
            log_probs = [np.random.randn() for _ in range(10)]
            rewards = [np.random.randn() for _ in range(10)]
            dones = [False] * 9 + [True]
            
            trainer.update(states, actions, log_probs, rewards, dones, epochs=1)
            
            # Save agent
            agent1.save(str(model_path))
            
            # Create new agent and load
            agent2 = PPOAgent(state_dim, action_dim, device='cpu')
            agent2.load(str(model_path))
            
            # Test that both agents produce same outputs
            test_state = np.random.randn(*state_dim)
            
            action1, log_prob1 = agent1.get_action(test_state)
            action2, log_prob2 = agent2.get_action(test_state)
            
            value1 = agent1.get_value(test_state)
            value2 = agent2.get_value(test_state)
            
            # Should be very close (allowing for small numerical differences)
            assert abs(value1 - value2) < 1e-5
            
            env.close()
    
    def test_multiple_environments(self):
        """Test that the system works with different environments."""
        environments = ['MiniGrid-Empty-5x5-v0', 'MiniGrid-Empty-8x8-v0']
        
        for env_name in environments:
            try:
                env = MiniGridWrapper(env_name)
                
                # Test basic functionality
                state, _ = env.reset()
                assert state is not None
                
                action = env.action_space.sample()
                next_state, reward, terminated, truncated, info = env.step(action)
                assert next_state is not None
                
                # Test agent creation
                state_dim = env.observation_space.shape
                action_dim = env.action_space.n
                
                agent = PPOAgent(state_dim, action_dim, device='cpu')
                action, log_prob = agent.get_action(state)
                value = agent.get_value(state)
                
                assert isinstance(action, (int, np.integer))
                assert isinstance(log_prob, (float, np.floating))
                assert isinstance(value, (float, np.floating))
                
                env.close()
                
            except Exception as e:
                pytest.skip(f"Environment {env_name} not available: {e}")
    
    def test_training_convergence(self):
        """Test that training actually improves performance."""
        set_seed(42)
        
        # Create simple environment
        env = MiniGridWrapper('MiniGrid-Empty-5x5-v0', max_steps=50)
        state_dim = env.observation_space.shape
        action_dim = env.action_space.n
        
        # Create agent and trainer
        agent = PPOAgent(state_dim, action_dim, device='cpu')
        trainer = PPOTrainer(agent)
        
        # Evaluate initial performance
        initial_rewards = []
        for _ in range(5):
            state, _ = env.reset()
            episode_reward = 0
            done = False
            
            while not done:
                action, _ = agent.get_action(state)
                state, reward, terminated, truncated, _ = env.step(action)
                done = terminated or truncated
                episode_reward += reward
            
            initial_rewards.append(episode_reward)
        
        initial_mean = np.mean(initial_rewards)
        
        # Train for a few iterations
        for _ in range(10):
            # Collect trajectory
            states = []
            actions = []
            log_probs = []
            rewards = []
            dones = []
            
            state, _ = env.reset()
            
            for _ in range(50):
                action, log_prob = agent.get_action(state)
                next_state, reward, terminated, truncated, _ = env.step(action)
                done = terminated or truncated
                
                states.append(state)
                actions.append(action)
                log_probs.append(log_prob)
                rewards.append(reward)
                dones.append(done)
                
                if done:
                    state, _ = env.reset()
                else:
                    state = next_state
            
            # Update agent
            trainer.update(states, actions, log_probs, rewards, dones, epochs=2, batch_size=25)
        
        # Evaluate final performance
        final_rewards = []
        for _ in range(5):
            state, _ = env.reset()
            episode_reward = 0
            done = False
            
            while not done:
                action, _ = agent.get_action(state)
                state, reward, terminated, truncated, _ = env.step(action)
                done = terminated or truncated
                episode_reward += reward
            
            final_rewards.append(episode_reward)
        
        final_mean = np.mean(final_rewards)
        
        # Performance should improve (or at least not get much worse)
        # Note: This is a very basic test and might be flaky
        print(f"Initial mean reward: {initial_mean:.3f}")
        print(f"Final mean reward: {final_mean:.3f}")
        
        # At minimum, the agent should not completely break
        assert not np.isnan(final_mean)
        assert not np.isinf(final_mean)
        
        env.close()
    
    def test_error_handling(self):
        """Test error handling in various scenarios."""
        # Test invalid environment
        with pytest.raises(Exception):
            env = MiniGridWrapper('NonExistentEnvironment-v0')
        
        # Test invalid model loading
        agent = PPOAgent((147,), 7, device='cpu')
        with pytest.raises(Exception):
            agent.load('nonexistent_model.pt')
        
        # Test invalid config file
        with pytest.raises(Exception):
            config = Config('nonexistent_config.yaml')


class TestPerformanceBenchmarks:
    """Basic performance benchmarks."""
    
    def test_training_speed(self):
        """Test that training completes in reasonable time."""
        import time
        
        set_seed(42)
        
        env = MiniGridWrapper('MiniGrid-Empty-5x5-v0')
        state_dim = env.observation_space.shape
        action_dim = env.action_space.n
        
        agent = PPOAgent(state_dim, action_dim, device='cpu')
        trainer = PPOTrainer(agent)
        
        # Time a small training update
        start_time = time.time()
        
        # Generate data
        states = [np.random.randn(*state_dim) for _ in range(100)]
        actions = [np.random.randint(0, action_dim) for _ in range(100)]
        log_probs = [np.random.randn() for _ in range(100)]
        rewards = [np.random.randn() for _ in range(100)]
        dones = [False] * 99 + [True]
        
        # Update
        trainer.update(states, actions, log_probs, rewards, dones, epochs=2, batch_size=32)
        
        elapsed_time = time.time() - start_time
        
        # Should complete in reasonable time (less than 10 seconds on CPU)
        assert elapsed_time < 10.0
        
        print(f"Training update took {elapsed_time:.3f} seconds")
        
        env.close()
    
    def test_memory_usage(self):
        """Test that memory usage is reasonable."""
        # This is a basic test - in practice you'd use memory profiling tools
        
        env = MiniGridWrapper('MiniGrid-Empty-5x5-v0')
        state_dim = env.observation_space.shape
        action_dim = env.action_space.n
        
        agent = PPOAgent(state_dim, action_dim, device='cpu')
        
        # Count parameters
        total_params = sum(p.numel() for p in agent.policy_net.parameters())
        total_params += sum(p.numel() for p in agent.value_net.parameters())
        
        # Should be reasonable number of parameters (less than 1M for this simple case)
        assert total_params < 1_000_000
        
        print(f"Total parameters: {total_params:,}")
        
        env.close()


if __name__ == "__main__":
    pytest.main([__file__])
