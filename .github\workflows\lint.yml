name: <PERSON><PERSON> and Format Check

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]

jobs:
  lint:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'

    - name: Install uv
      uses: astral-sh/setup-uv@v4
      with:
        version: "latest"

    - name: Install dependencies
      run: |
        uv sync --dev

    - name: Check formatting with black
      run: |
        uv run black --check --diff llmae_ppo/ main.py evaluate.py

    - name: Check import sorting with isort
      run: |
        uv run isort --check-only --diff llmae_ppo/ main.py evaluate.py

    - name: Lint with flake8
      run: |
        uv run flake8 llmae_ppo/ main.py evaluate.py

    - name: Run pre-commit hooks
      run: |
        uv run pre-commit run --all-files
