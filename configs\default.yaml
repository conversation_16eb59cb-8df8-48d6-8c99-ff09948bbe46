# Default configuration for PPO training on MiniGrid

# Environment settings
env_name: 'MiniGrid-Empty-8x8-v0'
flatten_obs: true
normalize_obs: true
max_episode_steps: 1000

# Training settings
total_timesteps: 1000000
learning_rate: 0.0003
batch_size: 64
n_epochs: 4
n_steps: 2048
gamma: 0.99
gae_lambda: 0.95
clip_epsilon: 0.2
value_coef: 0.5
entropy_coef: 0.01
max_grad_norm: 0.5

# Network settings
hidden_dim: 256

# Logging and saving
log_interval: 10
save_interval: 100
eval_interval: 50
eval_episodes: 10
save_dir: 'checkpoints'
log_dir: 'logs'
use_wandb: false
wandb_project: 'llmae-ppo'

# Device settings
device: 'cuda'  # or 'cpu'
seed: 42
